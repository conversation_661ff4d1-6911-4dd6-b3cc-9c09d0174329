/// 数据库服务提供者使用示例
///
/// 展示如何使用优化后的数据库服务提供者进行数据库操作
use rustravel::app::providers::DatabaseServiceProvider;
use rustravel::config::DatabaseConfig;
use std::sync::Arc;
use tracing::{error, info};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    info!("开始数据库服务提供者示例");

    // 1. 创建数据库配置
    let db_config = DatabaseConfig::from_env();
    info!("数据库配置加载完成");

    // 2. 创建数据库服务提供者
    let db_provider = Arc::new(DatabaseServiceProvider::new(&db_config));
    info!("数据库服务提供者创建完成");

    // 3. 启动数据库服务提供者（包括连接池初始化、预热和监控）
    match db_provider.boot().await {
        Ok(_) => info!("数据库服务提供者启动成功"),
        Err(e) => {
            error!("数据库服务提供者启动失败: {}", e);
            return Err(e);
        }
    }

    // 4. 检查连接池状态
    let status = db_provider.get_status().await;
    info!("连接池状态: {:?}", status);

    // 5. 获取连接池统计信息
    let stats = db_provider.get_stats().await;
    info!("连接池统计: {:?}", stats);

    // 6. 检查连接池健康状态
    let is_healthy = db_provider.is_healthy().await;
    info!("连接池健康状态: {}", is_healthy);

    // 7. 获取详细的连接池信息
    let pool_info = db_provider.get_pool_info().await;
    info!("连接池详细信息:\n{}", pool_info);

    // 8. 使用连接池执行数据库操作
    match db_provider
        .get_connection(|mut conn| async move {
            info!("成功获取数据库连接");

            // 执行简单查询
            match conn.simple_query("SELECT 1 as test_value").await {
                Ok(mut stream) => {
                    info!("查询执行成功");

                    // 处理查询结果
                    use futures::TryStreamExt;
                    while let Ok(Some(row)) = stream.try_next().await {
                        if let Some(value) = row.get::<i32, _>("test_value") {
                            info!("查询结果: {}", value);
                        }
                    }
                    Ok(())
                }
                Err(e) => {
                    error!("查询执行失败: {}", e);
                    Err(e.into())
                }
            }
        })
        .await
    {
        Ok(_) => info!("数据库操作完成"),
        Err(e) => error!("获取数据库连接失败: {}", e),
    }

    // 9. 等待一段时间，观察监控日志
    info!("等待30秒，观察健康检查日志...");
    tokio::time::sleep(tokio::time::Duration::from_secs(30)).await;

    // 10. 再次检查统计信息
    let final_stats = db_provider.get_stats().await;
    info!("最终连接池统计: {:?}", final_stats);

    info!("数据库服务提供者示例完成");
    Ok(())
}

/// 高级使用示例：批量操作
async fn batch_operations_example(
    db_provider: Arc<DatabaseServiceProvider>,
) -> Result<(), Box<dyn std::error::Error>> {
    info!("开始批量操作示例");

    // 并发获取多个连接进行操作
    let mut handles = Vec::new();

    for i in 0..5 {
        let provider = Arc::clone(&db_provider);
        handles.push(tokio::spawn(async move {
            match provider
                .get_connection(|mut conn| async move {
                    info!("任务 {} 获取连接成功", i);

                    // 模拟数据库操作
                    match conn.simple_query("SELECT GETDATE() as current_time").await {
                        Ok(_) => {
                            info!("任务 {} 执行成功", i);
                            Ok(())
                        }
                        Err(e) => {
                            error!("任务 {} 执行失败: {}", i, e);
                            Err(e.into())
                        }
                    }
                })
                .await
            {
                Ok(_) => {}
                Err(e) => error!("任务 {} 获取连接失败: {}", i, e),
            }
        }));
    }

    // 等待所有任务完成
    for handle in handles {
        let _ = handle.await;
    }

    info!("批量操作示例完成");
    Ok(())
}

/// 错误处理示例
async fn error_handling_example() -> Result<(), Box<dyn std::error::Error>> {
    info!("开始错误处理示例");

    // 使用错误的配置创建数据库服务提供者
    let mut db_config = DatabaseConfig::from_env();
    db_config.host = "invalid_host".to_string();
    db_config.port = 9999;

    let db_provider = Arc::new(DatabaseServiceProvider::new(&db_config));

    // 尝试启动（应该失败）
    match db_provider.boot().await {
        Ok(_) => info!("意外成功（这不应该发生）"),
        Err(e) => {
            info!("预期的错误: {}", e);

            // 检查错误状态
            let status = db_provider.get_status().await;
            info!("错误状态: {:?}", status);
        }
    }

    info!("错误处理示例完成");
    Ok(())
}
