/// 数据库服务提供者
///
/// 负责初始化和配置数据库连接池。
/// 使用 bb8 作为连接池管理器，tiberius 作为 SQL Server 驱动。
///
/// 优化特性：
/// - 统一的连接池管理
/// - 完整的配置参数支持
/// - 连接池预热功能
/// - 健康检查和监控
/// - 优雅的错误处理
use bb8::Pool;
use bb8_tiberius::ConnectionManager;
use tracing::{error, info, warn};

use crate::config::DatabaseConfig;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Mutex;
use tokio::time;

/// 数据库连接池状态
#[derive(Debug, Clone)]
pub enum PoolStatus {
    /// 未初始化
    Uninitialized,
    /// 初始化中
    Initializing,
    /// 运行中
    Running,
    /// 错误状态
    Error(String),
}

/// 数据库连接池统计信息
#[derive(Debug, Clone)]
pub struct PoolStats {
    /// 总连接数
    pub total_connections: u32,
    /// 活跃连接数
    pub active_connections: u32,
    /// 空闲连接数
    pub idle_connections: u32,
    /// 等待连接的请求数
    pub waiting_requests: u32,
    /// 最后健康检查时间
    pub last_health_check: Option<Instant>,
    /// 健康检查状态
    pub health_status: bool,
}

/// 数据库服务提供者结构体
///
/// 该结构体负责创建和配置数据库连接池。
/// 连接池配置包括：
/// - 最大连接数
/// - 最小空闲连接数
/// - 连接超时时间
/// - 连接池预热
/// - 健康检查和监控
#[derive(Clone)]
pub struct DatabaseServiceProvider {
    /// 数据库配置
    config: DatabaseConfig,
    /// bb8 连接池
    pool: Arc<Mutex<Option<Pool<ConnectionManager>>>>,
    /// 连接池状态
    status: Arc<Mutex<PoolStatus>>,
    /// 连接池统计信息
    stats: Arc<Mutex<PoolStats>>,
}

impl DatabaseServiceProvider {
    /// 初始化数据库连接池
    ///
    /// # 返回
    ///
    /// 返回一个数据库连接池实例的 Result
    ///
    /// # 错误
    ///
    /// - 如果无法创建数据库连接池，返回错误而不是 panic
    pub async fn init() -> Result<Pool<ConnectionManager>, Box<dyn std::error::Error + Send + Sync>>
    {
        // 创建数据库配置
        let db_config = DatabaseConfig::from_env();

        // 验证数据库连接类型
        if db_config.connection != "sqlsrv" {
            return Err(format!(
                "不支持的数据库连接类型: {}，当前仅支持 'sqlsrv'",
                db_config.connection
            )
            .into());
        }

        info!("使用数据库连接类型: {}", db_config.connection);

        // 使用配置层的连接字符串方法，确保格式一致
        let conn_str = db_config.connection_string();

        // 转换为 tiberius 兼容的连接字符串格式
        let tiberius_conn_str = conn_str
            .replace("jdbc:sqlserver://", "server=")
            .replace(";databaseName=", ";database=")
            .replace(";user=", ";user id=")
            .replace(";encrypt=true", ";Encrypt=true")
            .replace(
                ";trustServerCertificate=true",
                ";TrustServerCertificate=true",
            )
            .replace(";integratedSecurity=false", ";Integrated Security=false")
            .replace(
                ";columnEncryptionSetting=Enabled",
                ";Column Encryption Setting=Enabled",
            )
            .replace(";multiSubnetFailover=true", ";MultiSubnetFailover=True")
            .replace(
                ";applicationIntent=ReadWrite",
                ";Application Intent=ReadWrite",
            )
            .replace(";authentication=SqlPassword", ";Authentication=SqlPassword");

        info!("数据库连接字符串: {}", tiberius_conn_str);

        // 创建连接管理器
        let manager = ConnectionManager::build(tiberius_conn_str.as_str()).map_err(|e| {
            error!("创建数据库连接管理器失败: {}", e);
            error!("请检查以下内容:");
            error!("1. SQL Server 服务是否正在运行");
            error!("2. 用户名和密码是否正确");
            error!("3. SQL Server 是否允许 SQL Server 身份验证");
            error!("4. 防火墙是否允许连接");
            error!("5. 端口是否正确");
            e
        })?;

        info!("数据库连接管理器创建成功");

        // 创建连接池
        let pool = Pool::builder()
            .max_size(db_config.pool_max_size)
            .min_idle(Some(db_config.pool_min_idle))
            .connection_timeout(Duration::from_secs(db_config.pool_timeout))
            .build(manager)
            .await
            .map_err(|e| {
                error!("创建数据库连接池失败: {}", e);
                error!("请检查以下内容:");
                error!("1. 连接池参数是否合理");
                error!("2. 数据库服务器负载是否过高");
                error!("3. 网络连接是否稳定");
                e
            })?;

        info!(
            "数据库连接池创建成功，最大连接数: {}, 最小空闲连接数: {}, 连接超时: {}秒",
            db_config.pool_max_size, db_config.pool_min_idle, db_config.pool_timeout
        );

        Ok(pool)
    }

    /// 创建数据库服务提供者实例
    pub fn new(config: &DatabaseConfig) -> Self {
        Self {
            config: config.clone(),
            pool: Arc::new(Mutex::new(None)),
            status: Arc::new(Mutex::new(PoolStatus::Uninitialized)),
            stats: Arc::new(Mutex::new(PoolStats {
                total_connections: 0,
                active_connections: 0,
                idle_connections: 0,
                waiting_requests: 0,
                last_health_check: None,
                health_status: false,
            })),
        }
    }

    /// 启动数据库服务提供者
    ///
    /// 该方法会：
    /// 1. 初始化连接池
    /// 2. 预热连接池
    /// 3. 启动健康检查监控
    pub async fn boot(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 更新状态为初始化中
        *self.status.lock().await = PoolStatus::Initializing;

        info!("开始初始化数据库连接池...");

        // 初始化连接池
        match Self::init().await {
            Ok(pool) => {
                *self.pool.lock().await = Some(pool);
                *self.status.lock().await = PoolStatus::Running;
                info!("数据库连接池初始化成功");
            }
            Err(e) => {
                let error_msg = format!("数据库连接池初始化失败: {}", e);
                error!("{}", error_msg);
                *self.status.lock().await = PoolStatus::Error(error_msg.clone());
                return Err(e);
            }
        }

        // 预热连接池
        if let Err(e) = self.warmup_pool().await {
            warn!("连接池预热失败: {}", e);
        }

        // 启动监控
        self.start_monitoring().await;

        info!("数据库服务提供者启动完成");
        Ok(())
    }

    /// 预热连接池
    ///
    /// 并发创建指定数量的连接进行预热，提高应用启动后的响应速度
    async fn warmup_pool(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let pool_guard = self.pool.lock().await;
        if let Some(pool) = &*pool_guard {
            let warmup_count = self.config.pool_warmup;
            info!("开始预热数据库连接池，预热数量: {}", warmup_count);

            let mut handles = Vec::new();
            for i in 0..warmup_count {
                let pool_clone = pool.clone();
                handles.push(tokio::spawn(async move {
                    let start = Instant::now();
                    match pool_clone.get().await {
                        Ok(mut conn) => {
                            // 执行一个简单的查询来验证连接
                            match conn.simple_query("SELECT 1").await {
                                Ok(_) => {
                                    info!(
                                        "连接池预热 #{} 成功，耗时: {:?}",
                                        i + 1,
                                        start.elapsed()
                                    );
                                }
                                Err(e) => {
                                    error!("连接池预热 #{} 查询失败: {}", i + 1, e);
                                }
                            }
                        }
                        Err(e) => {
                            error!("连接池预热 #{} 获取连接失败: {}", i + 1, e);
                        }
                    }
                }));
            }

            // 等待所有预热任务完成
            let mut success_count = 0;
            for handle in handles {
                if handle.await.is_ok() {
                    success_count += 1;
                }
            }

            info!("连接池预热完成，成功: {}/{}", success_count, warmup_count);

            if success_count == 0 {
                return Err("所有连接池预热都失败了".into());
            }
        } else {
            return Err("连接池未初始化".into());
        }

        Ok(())
    }

    /// 启动连接池监控
    ///
    /// 定期执行健康检查，监控连接池状态和性能
    async fn start_monitoring(&self) {
        let pool = Arc::clone(&self.pool);
        let stats = Arc::clone(&self.stats);
        let status = Arc::clone(&self.status);
        let config = self.config.clone();
        let interval = Duration::from_secs(config.pool_monitor_interval);
        let timeout = Duration::from_secs(config.pool_health_check_timeout);

        info!(
            "启动数据库连接池监控，检查间隔: {}秒，超时: {}秒",
            config.pool_monitor_interval, config.pool_health_check_timeout
        );

        tokio::spawn(async move {
            let mut interval_timer = time::interval(interval);
            loop {
                interval_timer.tick().await;

                let pool_guard = pool.lock().await;
                if let Some(db_pool) = &*pool_guard {
                    let start = Instant::now();

                    // 执行健康检查
                    match db_pool.get().await {
                        Ok(mut conn) => {
                            // 执行简单查询验证连接
                            match conn.simple_query("SELECT 1").await {
                                Ok(_) => {
                                    let elapsed = start.elapsed();
                                    let is_timeout = elapsed > timeout;

                                    // 更新统计信息
                                    {
                                        let mut stats_guard = stats.lock().await;
                                        stats_guard.last_health_check = Some(start);
                                        stats_guard.health_status = !is_timeout;

                                        // 获取连接池状态信息
                                        let state = db_pool.state();
                                        stats_guard.total_connections = state.connections;
                                        stats_guard.idle_connections = state.idle_connections;
                                    }

                                    if is_timeout {
                                        warn!("数据库连接池健康检查超时: {:?}", elapsed);
                                    } else {
                                        info!("数据库连接池健康检查正常，耗时: {:?}", elapsed);
                                    }
                                }
                                Err(e) => {
                                    error!("数据库连接池健康检查查询失败: {}", e);
                                    let mut stats_guard = stats.lock().await;
                                    stats_guard.health_status = false;
                                    stats_guard.last_health_check = Some(start);
                                }
                            }
                        }
                        Err(e) => {
                            error!("数据库连接池健康检查获取连接失败: {}", e);
                            let mut stats_guard = stats.lock().await;
                            stats_guard.health_status = false;
                            stats_guard.last_health_check = Some(start);

                            // 如果连接失败，更新状态为错误
                            *status.lock().await =
                                PoolStatus::Error(format!("健康检查失败: {}", e));
                        }
                    }
                } else {
                    warn!("数据库连接池未初始化，跳过健康检查");
                }
            }
        });
    }

    /// 获取连接池实例
    pub async fn get_pool(&self) -> Option<Pool<ConnectionManager>> {
        let pool = self.pool.lock().await;
        pool.clone()
    }

    /// 获取连接池状态
    pub async fn get_status(&self) -> PoolStatus {
        let status = self.status.lock().await;
        status.clone()
    }

    /// 获取连接池统计信息
    pub async fn get_stats(&self) -> PoolStats {
        let stats = self.stats.lock().await;
        stats.clone()
    }

    /// 获取数据库连接
    ///
    /// 这是一个便捷方法，使用回调函数执行数据库操作，无需手动管理连接的生命周期。
    /// 该方法接受一个回调函数，该函数接收一个连接并返回一个 Future。
    ///
    /// # 参数
    /// - `f`: 一个接收连接并返回 Future 的函数
    ///
    /// # 返回值
    /// - 回调函数的返回值，如果获取连接失败，则返回错误
    ///
    /// # 示例
    /// ```rust
    /// let result = db_provider.get_connection(|mut conn| async move {
    ///     conn.simple_query("SELECT 1").await
    /// }).await?;
    /// ```
    pub async fn get_connection<F, Fut, R>(
        &self,
        f: F,
    ) -> Result<R, Box<dyn std::error::Error + Send + Sync>>
    where
        F: FnOnce(bb8::PooledConnection<'_, ConnectionManager>) -> Fut,
        Fut: std::future::Future<Output = R>,
    {
        let pool_guard = self.pool.lock().await;
        if let Some(pool) = &*pool_guard {
            let conn = pool.get().await.map_err(|e| {
                error!("从连接池获取连接失败: {}", e);
                Box::new(e) as Box<dyn std::error::Error + Send + Sync>
            })?;
            Ok(f(conn).await)
        } else {
            Err("连接池未初始化".into())
        }
    }

    /// 获取数据库连接池
    ///
    /// 返回连接池的克隆，调用者可以直接使用连接池获取连接。
    /// 这个方法适用于需要直接管理连接生命周期的场景。
    ///
    /// # 返回值
    /// - `Pool<ConnectionManager>`: 连接池实例
    ///
    /// # 错误
    /// - 如果连接池未初始化，返回错误
    ///
    /// # 示例
    /// ```rust
    /// let pool = db_provider.get_connection_pool().await?;
    /// let conn = pool.get().await?;
    /// // 使用连接...
    /// ```
    pub async fn get_connection_pool(
        &self,
    ) -> Result<Pool<ConnectionManager>, Box<dyn std::error::Error + Send + Sync>> {
        let pool_guard = self.pool.lock().await;
        if let Some(pool) = &*pool_guard {
            Ok(pool.clone())
        } else {
            Err("连接池未初始化".into())
        }
    }

    /// 检查连接池是否健康
    pub async fn is_healthy(&self) -> bool {
        let stats = self.stats.lock().await;
        stats.health_status
    }

    /// 获取连接池详细信息（用于调试和监控）
    pub async fn get_pool_info(&self) -> String {
        let status = self.get_status().await;
        let stats = self.get_stats().await;

        format!(
            "数据库连接池信息:\n\
             状态: {:?}\n\
             总连接数: {}\n\
             活跃连接数: {}\n\
             空闲连接数: {}\n\
             等待请求数: {}\n\
             健康状态: {}\n\
             最后健康检查: {:?}\n\
             配置信息:\n\
             - 最大连接数: {}\n\
             - 最小空闲连接数: {}\n\
             - 连接超时: {}秒\n\
             - 预热数量: {}\n\
             - 监控间隔: {}秒\n\
             - 健康检查超时: {}秒",
            status,
            stats.total_connections,
            stats.active_connections,
            stats.idle_connections,
            stats.waiting_requests,
            stats.health_status,
            stats.last_health_check,
            self.config.pool_max_size,
            self.config.pool_min_idle,
            self.config.pool_timeout,
            self.config.pool_warmup,
            self.config.pool_monitor_interval,
            self.config.pool_health_check_timeout
        )
    }
}
