[package]
name = "rustravel"
version = "0.1.0"
edition = "2021"

[lib]
path = "src/lib.rs"

[[bin]]
name = "rustravel"
path = "src/main.rs"

# 构建依赖
[build-dependencies]
chrono = "0.4"

# 依赖库
[dependencies]
# Web框架
axum = "0.8.4"
tower = "0.4"
tower-http = { version = "0.5", features = ["trace"] }
reqwest = { version = "0.11", features = [
    "json",
    "native-tls",
], default-features = false }

# 数据库
tiberius = { version = "0.12.3", features = [
    "chrono",
], default-features = false }
bb8 = "0.8.6"
bb8-tiberius = { version = "0.16", features = [
    "tls",
], default-features = false }
tokio-util = { version = "0.7", features = ["compat"] }

# 缓存
redis = { version = "0.24", features = ["tokio-comp", "connection-manager"] }
bb8-redis = "0.10.0"

# 邮件
lettre = { version = "0.11", features = [
    "tokio1",
    "tokio1-native-tls",
], default-features = false }

# 异步运行时
tokio = { version = "1.36.0", features = ["full"] }

# 日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "time"] }
tracing-appender = "0.2"
log = "0.4"
env_logger = "0.11"
chrono = { version = "0.4.34", features = ["serde", "clock"] }
flate2 = "1.0"
time = { version = "0.3", features = ["macros", "local-offset"] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_derive = "1.0"

# 配置
dotenvy = "0.15"

# 工具库
once_cell = "1.19"
rand = "0.8"
regex = "1.11.1"
futures-util = "0.3"
futures = "0.3"
async-trait = "0.1"

# 模板引擎
askama = "0.14.0"

# UUID
uuid = { version = "1.7", features = ["v4", "serde"] }

# 错误处理
thiserror = "1.0"
anyhow = "1.0"

# 其他
chrono-tz = "0.8"
socket2 = "0.5.10"
encoding = "0.2.33"

[dev-dependencies]
criterion = "0.7.0"

[package.metadata.askama]
template_dirs = ["templates"]
