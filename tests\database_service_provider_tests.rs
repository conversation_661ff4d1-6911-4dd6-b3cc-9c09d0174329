/// 数据库服务提供者测试
///
/// 测试优化后的数据库服务提供者的各项功能
use rustravel::app::providers::{DatabaseServiceProvider, PoolStatus};
use rustravel::config::DatabaseConfig;
use std::sync::Arc;
use tokio::time::{sleep, Duration};

/// 测试数据库服务提供者的基本功能
#[tokio::test]
async fn test_database_service_provider_basic_functionality() {
    // 创建测试配置
    let mut config = create_test_config();
    config.pool_max_size = 5;
    config.pool_min_idle = 2;
    config.pool_warmup = 3;

    let provider = Arc::new(DatabaseServiceProvider::new(&config));

    // 测试初始状态
    let initial_status = provider.get_status().await;
    assert!(matches!(initial_status, PoolStatus::Uninitialized));

    // 测试启动（注意：这个测试需要真实的数据库连接）
    // 在实际环境中，这里应该会成功
    // 在测试环境中，我们主要测试错误处理
    let boot_result = provider.boot().await;

    // 如果数据库不可用，应该返回错误而不是 panic
    if boot_result.is_err() {
        let status = provider.get_status().await;
        assert!(matches!(status, PoolStatus::Error(_)));
    }
}

/// 测试配置参数验证
#[tokio::test]
async fn test_configuration_validation() {
    // 测试不支持的数据库类型
    let result = DatabaseServiceProvider::init().await;

    // 这个测试主要验证错误处理机制
    // 在没有正确数据库配置的情况下，应该返回错误而不是 panic
    if result.is_err() {
        println!("预期的配置错误: {:?}", result.err());
    }
}

/// 测试连接池统计信息
#[tokio::test]
async fn test_pool_statistics() {
    let config = create_test_config();
    let provider = Arc::new(DatabaseServiceProvider::new(&config));

    // 测试初始统计信息
    let stats = provider.get_stats().await;
    assert_eq!(stats.total_connections, 0);
    assert_eq!(stats.active_connections, 0);
    assert_eq!(stats.idle_connections, 0);
    assert_eq!(stats.waiting_requests, 0);
    assert!(!stats.health_status);
    assert!(stats.last_health_check.is_none());
}

/// 测试连接池状态管理
#[tokio::test]
async fn test_pool_status_management() {
    let config = create_test_config();
    let provider = Arc::new(DatabaseServiceProvider::new(&config));

    // 测试状态转换
    let status = provider.get_status().await;
    assert!(matches!(status, PoolStatus::Uninitialized));

    // 测试健康检查
    let is_healthy = provider.is_healthy().await;
    assert!(!is_healthy);
}

/// 测试连接池信息格式化
#[tokio::test]
async fn test_pool_info_formatting() {
    let config = create_test_config();
    let provider = Arc::new(DatabaseServiceProvider::new(&config));

    let pool_info = provider.get_pool_info().await;

    // 验证信息包含所有必要的字段
    assert!(pool_info.contains("数据库连接池信息"));
    assert!(pool_info.contains("状态:"));
    assert!(pool_info.contains("总连接数:"));
    assert!(pool_info.contains("配置信息:"));
    assert!(pool_info.contains("最大连接数:"));
    assert!(pool_info.contains("监控间隔:"));
}

/// 测试配置参数的完整性
#[tokio::test]
async fn test_configuration_completeness() {
    let config = create_test_config();

    // 验证所有配置参数都有合理的默认值
    assert!(!config.connection.is_empty());
    assert!(!config.host.is_empty());
    assert!(config.port > 0);
    assert!(!config.database.is_empty());
    assert!(!config.username.is_empty());
    assert!(config.pool_max_size > 0);
    assert!(config.pool_min_idle <= config.pool_max_size);
    assert!(config.pool_timeout > 0);
    assert!(config.pool_warmup <= config.pool_max_size);
    assert!(config.pool_monitor_interval > 0);
    assert!(config.pool_health_check_timeout > 0);
}

/// 测试并发连接获取
#[tokio::test]
async fn test_concurrent_connection_requests() {
    let config = create_test_config();
    let provider = Arc::new(DatabaseServiceProvider::new(&config));

    // 测试并发获取连接（在没有真实数据库的情况下应该都失败）
    let mut handles = Vec::new();

    for i in 0..5 {
        let provider_clone = Arc::clone(&provider);
        handles.push(tokio::spawn(async move {
            let result = provider_clone
                .get_connection(|_conn| async move {
                    Ok::<(), Box<dyn std::error::Error + Send + Sync>>(())
                })
                .await;
            println!("连接请求 {} 结果: {:?}", i, result.is_ok());
            result.is_err() // 在测试环境中，预期会失败
        }));
    }

    // 等待所有任务完成
    for handle in handles {
        let result = handle.await.unwrap();
        assert!(result); // 在没有数据库的情况下，应该都失败
    }
}

/// 测试错误处理的优雅性
#[tokio::test]
async fn test_graceful_error_handling() {
    // 使用无效配置
    let mut config = create_test_config();
    config.host = "invalid_host_12345".to_string();
    config.port = 99999;

    let provider = Arc::new(DatabaseServiceProvider::new(&config));

    // 测试启动失败的处理
    let boot_result = provider.boot().await;
    assert!(boot_result.is_err());

    // 验证错误状态
    let status = provider.get_status().await;
    assert!(matches!(status, PoolStatus::Error(_)));

    // 测试在错误状态下获取连接
    let conn_result = provider
        .get_connection(
            |_conn| async move { Ok::<(), Box<dyn std::error::Error + Send + Sync>>(()) },
        )
        .await;
    assert!(conn_result.is_err());
}

/// 创建测试配置
fn create_test_config() -> DatabaseConfig {
    DatabaseConfig {
        connection: "sqlsrv".to_string(),
        host: "localhost".to_string(),
        port: 1433,
        database: "test_db".to_string(),
        username: "test_user".to_string(),
        password: "test_password".to_string(),
        pool_max_size: 10,
        pool_min_idle: 2,
        pool_timeout: 5,
        pool_warmup: 3,
        pool_monitor_interval: 30,
        pool_health_check_timeout: 3,
    }
}

/// 性能测试：连接池创建时间
#[tokio::test]
async fn test_pool_creation_performance() {
    let config = create_test_config();

    let start = std::time::Instant::now();
    let provider = Arc::new(DatabaseServiceProvider::new(&config));
    let creation_time = start.elapsed();

    println!("连接池创建时间: {:?}", creation_time);

    // 验证创建时间合理（应该很快，因为只是创建结构体）
    assert!(creation_time < Duration::from_millis(100));

    // 测试获取初始状态的性能
    let start = std::time::Instant::now();
    let _status = provider.get_status().await;
    let status_time = start.elapsed();

    println!("获取状态时间: {:?}", status_time);
    assert!(status_time < Duration::from_millis(10));
}

/// 集成测试：完整的生命周期
#[tokio::test]
async fn test_complete_lifecycle() {
    let config = create_test_config();
    let provider = Arc::new(DatabaseServiceProvider::new(&config));

    // 1. 初始状态检查
    assert!(matches!(
        provider.get_status().await,
        PoolStatus::Uninitialized
    ));
    assert!(!provider.is_healthy().await);

    // 2. 尝试启动（在测试环境中可能失败）
    let boot_result = provider.boot().await;

    // 3. 检查最终状态
    let final_status = provider.get_status().await;
    if boot_result.is_ok() {
        assert!(matches!(final_status, PoolStatus::Running));
    } else {
        assert!(matches!(final_status, PoolStatus::Error(_)));
    }

    // 4. 获取详细信息
    let info = provider.get_pool_info().await;
    assert!(!info.is_empty());

    println!("完整生命周期测试完成");
}
