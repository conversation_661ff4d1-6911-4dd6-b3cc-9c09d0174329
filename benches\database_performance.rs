/// 数据库服务提供者性能基准测试
///
/// 测试优化后的数据库服务提供者的性能表现
use criterion::{black_box, criterion_group, criterion_main, Criterion};
use rustravel::app::providers::DatabaseServiceProvider;
use rustravel::config::DatabaseConfig;
use std::sync::Arc;
use tokio::runtime::Runtime;

/// 基准测试：数据库服务提供者创建性能
fn bench_provider_creation(c: &mut Criterion) {
    let config = create_benchmark_config();

    c.bench_function("provider_creation", |b| {
        b.iter(|| {
            let provider = DatabaseServiceProvider::new(black_box(&config));
            black_box(provider)
        })
    });
}

/// 基准测试：状态获取性能
fn bench_status_retrieval(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let config = create_benchmark_config();
    let provider = Arc::new(DatabaseServiceProvider::new(&config));

    c.bench_function("status_retrieval", |b| {
        b.to_async(&rt).iter(|| async {
            let status = provider.get_status().await;
            black_box(status)
        })
    });
}

/// 基准测试：统计信息获取性能
fn bench_stats_retrieval(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let config = create_benchmark_config();
    let provider = Arc::new(DatabaseServiceProvider::new(&config));

    c.bench_function("stats_retrieval", |b| {
        b.to_async(&rt).iter(|| async {
            let stats = provider.get_stats().await;
            black_box(stats)
        })
    });
}

/// 基准测试：连接池信息格式化性能
fn bench_pool_info_formatting(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let config = create_benchmark_config();
    let provider = Arc::new(DatabaseServiceProvider::new(&config));

    c.bench_function("pool_info_formatting", |b| {
        b.to_async(&rt).iter(|| async {
            let info = provider.get_pool_info().await;
            black_box(info)
        })
    });
}

/// 基准测试：并发状态访问性能
fn bench_concurrent_status_access(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let config = create_benchmark_config();
    let provider = Arc::new(DatabaseServiceProvider::new(&config));

    c.bench_function("concurrent_status_access", |b| {
        b.to_async(&rt).iter(|| async {
            let mut handles = Vec::new();

            // 创建10个并发任务
            for _ in 0..10 {
                let provider_clone = Arc::clone(&provider);
                handles.push(tokio::spawn(
                    async move { provider_clone.get_status().await },
                ));
            }

            // 等待所有任务完成
            let mut results = Vec::new();
            for handle in handles {
                results.push(handle.await.unwrap());
            }

            black_box(results)
        })
    });
}

/// 基准测试：配置参数访问性能
fn bench_config_access(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let config = create_benchmark_config();
    let provider = Arc::new(DatabaseServiceProvider::new(&config));

    c.bench_function("config_access", |b| {
        b.to_async(&rt).iter(|| async {
            // 通过获取连接池信息来间接测试配置访问性能
            let info = provider.get_pool_info().await;
            black_box(info.len()) // 只测试长度以避免字符串比较开销
        })
    });
}

/// 基准测试：健康检查性能
fn bench_health_check(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let config = create_benchmark_config();
    let provider = Arc::new(DatabaseServiceProvider::new(&config));

    c.bench_function("health_check", |b| {
        b.to_async(&rt).iter(|| async {
            let is_healthy = provider.is_healthy().await;
            black_box(is_healthy)
        })
    });
}

/// 基准测试：连接获取尝试性能（预期失败）
fn bench_connection_attempt(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let config = create_benchmark_config();
    let provider = Arc::new(DatabaseServiceProvider::new(&config));

    c.bench_function("connection_attempt", |b| {
        b.to_async(&rt).iter(|| async {
            // 在没有真实数据库的情况下，这应该快速失败
            let result = provider
                .get_connection(|_conn| async move {
                    Ok::<(), Box<dyn std::error::Error + Send + Sync>>(())
                })
                .await;
            black_box(result.is_err())
        })
    });
}

/// 基准测试：内存使用效率
fn bench_memory_efficiency(c: &mut Criterion) {
    c.bench_function("memory_efficiency", |b| {
        b.iter(|| {
            let config = create_benchmark_config();
            let mut providers = Vec::new();

            // 创建多个提供者实例来测试内存效率
            for _ in 0..100 {
                providers.push(DatabaseServiceProvider::new(black_box(&config)));
            }

            black_box(providers.len())
        })
    });
}

/// 基准测试：配置克隆性能
fn bench_config_cloning(c: &mut Criterion) {
    let config = create_benchmark_config();

    c.bench_function("config_cloning", |b| {
        b.iter(|| {
            let cloned = black_box(&config).clone();
            black_box(cloned)
        })
    });
}

/// 基准测试：Arc 克隆性能
fn bench_arc_cloning(c: &mut Criterion) {
    let config = create_benchmark_config();
    let provider = Arc::new(DatabaseServiceProvider::new(&config));

    c.bench_function("arc_cloning", |b| {
        b.iter(|| {
            let cloned = Arc::clone(black_box(&provider));
            black_box(cloned)
        })
    });
}

/// 创建基准测试配置
fn create_benchmark_config() -> DatabaseConfig {
    DatabaseConfig {
        connection: "sqlsrv".to_string(),
        host: "localhost".to_string(),
        port: 1433,
        database: "benchmark_db".to_string(),
        username: "benchmark_user".to_string(),
        password: "benchmark_password".to_string(),
        pool_max_size: 20,
        pool_min_idle: 5,
        pool_timeout: 10,
        pool_warmup: 5,
        pool_monitor_interval: 60,
        pool_health_check_timeout: 5,
    }
}

/// 压力测试：大量并发操作
fn bench_high_concurrency(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let config = create_benchmark_config();
    let provider = Arc::new(DatabaseServiceProvider::new(&config));

    c.bench_function("high_concurrency", |b| {
        b.to_async(&rt).iter(|| async {
            let mut handles = Vec::new();

            // 创建100个并发任务
            for i in 0..100 {
                let provider_clone = Arc::clone(&provider);
                handles.push(tokio::spawn(async move {
                    match i % 4 {
                        0 => provider_clone.get_status().await.clone(),
                        1 => {
                            let stats = provider_clone.get_stats().await;
                            format!("{:?}", stats)
                        }
                        2 => {
                            let healthy = provider_clone.is_healthy().await;
                            healthy.to_string()
                        }
                        _ => provider_clone.get_pool_info().await,
                    }
                }));
            }

            // 等待所有任务完成
            let mut results = Vec::new();
            for handle in handles {
                results.push(handle.await.unwrap());
            }

            black_box(results.len())
        })
    });
}

criterion_group!(
    benches,
    bench_provider_creation,
    bench_status_retrieval,
    bench_stats_retrieval,
    bench_pool_info_formatting,
    bench_concurrent_status_access,
    bench_config_access,
    bench_health_check,
    bench_connection_attempt,
    bench_memory_efficiency,
    bench_config_cloning,
    bench_arc_cloning,
    bench_high_concurrency
);

criterion_main!(benches);
